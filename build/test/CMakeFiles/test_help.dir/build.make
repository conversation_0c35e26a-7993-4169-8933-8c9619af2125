# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

# Utility rule file for test_help.

# Include the progress variables for this target.
include test/CMakeFiles/test_help.dir/progress.make

test/CMakeFiles/test_help:
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo ""
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "=== Test Usage ==="
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Build tests:              make"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Run all tests (CTest):    make test"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Run tests verbose:        make run_tests"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Run tests direct (color): make run_tests_direct"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Run specific test:        ./bin/tests/<test_name>"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Clean test binaries:      make clean_tests"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo ""
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Test Output Differences:"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "  make test           - CTest format with numbers, no colors"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "  make run_tests_direct - Direct execution with colors"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "  ./bin/tests/xxx     - Individual test with colors"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo ""
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "⚠️  Important: Only run ONE test command at a time to avoid duplicate output!"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo ""
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "Available tests:"

test_help: test/CMakeFiles/test_help
test_help: test/CMakeFiles/test_help.dir/build.make
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "  - fd_test"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "  - fs_test"
	cd /home/<USER>/tbox/build/test && /usr/bin/cmake -E echo "  - string_test"
.PHONY : test_help

# Rule to build all files generated by this target.
test/CMakeFiles/test_help.dir/build: test_help

.PHONY : test/CMakeFiles/test_help.dir/build

test/CMakeFiles/test_help.dir/clean:
	cd /home/<USER>/tbox/build/test && $(CMAKE_COMMAND) -P CMakeFiles/test_help.dir/cmake_clean.cmake
.PHONY : test/CMakeFiles/test_help.dir/clean

test/CMakeFiles/test_help.dir/depend:
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/tbox /home/<USER>/tbox/test /home/<USER>/tbox/build /home/<USER>/tbox/build/test /home/<USER>/tbox/build/test/CMakeFiles/test_help.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : test/CMakeFiles/test_help.dir/depend

