#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/tbox/base/cabinet.hpp
cstdint
-
vector
-
limits
-
cabinet_token.h
/home/<USER>/tbox/base/cabinet_token.h

/home/<USER>/tbox/base/cabinet_token.h
cstdlib
-

/home/<USER>/tbox/base/defines.h

/home/<USER>/tbox/base/object_pool.hpp
cstdlib
-
new
-
limits
-
utility
-
limits
-
assert.h
-

/home/<USER>/tbox/event/common_loop.cpp
common_loop.h
/home/<USER>/tbox/event/common_loop.h
thread
-
unistd.h
-
signal.h
-
cinttypes
-
../base/defines.h
/home/<USER>/tbox/base/defines.h
fd_event.h
/home/<USER>/tbox/event/fd_event.h
stat.h
/home/<USER>/tbox/event/stat.h

/home/<USER>/tbox/event/common_loop.h
deque
-
mutex
-
thread
-
map
-
set
-
../base/cabinet.hpp
/home/<USER>/tbox/base/cabinet.hpp
../base/object_pool.hpp
/home/<USER>/tbox/base/object_pool.hpp
loop.h
/home/<USER>/tbox/event/loop.h
chrono
-

/home/<USER>/tbox/event/event.h
string
-

/home/<USER>/tbox/event/fd_event.h
functional
-
event.h
/home/<USER>/tbox/event/event.h

/home/<USER>/tbox/event/forward.h

/home/<USER>/tbox/event/loop.h
functional
-
chrono
-
string
-
vector
-
forward.h
/home/<USER>/tbox/event/forward.h
stat.h
/home/<USER>/tbox/event/stat.h

/home/<USER>/tbox/event/stat.h
cstdint
-
ostream
-

