cmake_minimum_required(VERSION 3.16)

project(tbox
    VERSION 1.0.0
    DESCRIPTION "C++ Toolbox Library"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(${CMAKE_SOURCE_DIR})

# 查找依赖
find_package(PkgConfig QUIET)

# 查找GTest (用于测试)
find_package(GTest QUIET)
if(NOT GTest_FOUND)
    # 如果系统没有安装GTest，尝试使用pkg-config
    if(PkgConfig_FOUND)
        pkg_check_modules(GTEST gtest)
        pkg_check_modules(GTEST_MAIN gtest_main)
    endif()
endif()

# 函数：自动发现并创建库（支持自动重新配置）
function(create_auto_library LIB_NAME LIB_DIR)
    # 使用CONFIGURE_DEPENDS自动检测文件变化并重新配置
    file(GLOB_RECURSE ${LIB_NAME}_SOURCES
        CONFIGURE_DEPENDS
        "${LIB_DIR}/*.cc"
        "${LIB_DIR}/*.cpp"
        "${LIB_DIR}/*.cxx"
        "${LIB_DIR}/*.c++"
    )
    file(GLOB_RECURSE ${LIB_NAME}_HEADERS
        CONFIGURE_DEPENDS
        "${LIB_DIR}/*.h"
        "${LIB_DIR}/*.hpp"
        "${LIB_DIR}/*.hxx"
        "${LIB_DIR}/*.h++"
    )

    # 如果有源文件，创建库
    if(${LIB_NAME}_SOURCES)
        add_library(${LIB_NAME} STATIC ${${LIB_NAME}_SOURCES} ${${LIB_NAME}_HEADERS})
        target_include_directories(${LIB_NAME} PUBLIC ${CMAKE_SOURCE_DIR})

        # 设置库的属性
        set_target_properties(${LIB_NAME} PROPERTIES
            VERSION ${PROJECT_VERSION}
            SOVERSION ${PROJECT_VERSION_MAJOR}
        )

        message(STATUS "Created library: ${LIB_NAME}")
        foreach(src ${${LIB_NAME}_SOURCES})
            get_filename_component(src_name ${src} NAME)
            message(STATUS "  - ${src_name}")
        endforeach()
    else()
        message(STATUS "No source files found for ${LIB_NAME} in ${LIB_DIR}")
    endif()
endfunction()

# 创建cmake目录和扫描脚本
file(MAKE_DIRECTORY "${CMAKE_SOURCE_DIR}/cmake")

# 创建util库
create_auto_library(tbox_util "util")

# 创建base库
create_auto_library(tbox_base "base")

# 创建base库
create_auto_library(tbox_event "event")

# 添加依赖关系：event库依赖base库
if(TARGET tbox_event AND TARGET tbox_base)
    target_link_libraries(tbox_event PUBLIC tbox_base)
endif()

# 启用测试
option(BUILD_TESTS "Build tests" ON)

if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(test)
endif()

# 安装配置
if(UTIL_SOURCES)
    install(TARGETS tbox_util
        ARCHIVE DESTINATION lib
        LIBRARY DESTINATION lib
        RUNTIME DESTINATION bin
    )
endif()

if(BASE_SOURCES)
    install(TARGETS tbox_base
        ARCHIVE DESTINATION lib
        LIBRARY DESTINATION lib
        RUNTIME DESTINATION bin
    )
endif()

# 安装头文件
install(DIRECTORY util/ DESTINATION include/tbox/util
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)

install(DIRECTORY base/ DESTINATION include/tbox/base
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)

# 打印配置信息
message(STATUS "")
message(STATUS "=== tbox Configuration Summary ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build tests: ${BUILD_TESTS}")
if(GTest_FOUND OR GTEST_FOUND)
    message(STATUS "GTest found: YES")
else()
    message(STATUS "GTest found: NO (tests will not be built)")
endif()
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "===================================")
message(STATUS "")
