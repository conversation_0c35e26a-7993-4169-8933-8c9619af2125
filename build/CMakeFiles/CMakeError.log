Performing C++ SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/tbox/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_572b9/fast && /usr/bin/make -f CMakeFiles/cmTC_572b9.dir/build.make CMakeFiles/cmTC_572b9.dir/build
make[1]: Entering directory '/home/<USER>/tbox/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_572b9.dir/src.cxx.o
/usr/bin/c++    -Wall -Wextra -Wpedantic -DCMAKE_HAVE_LIBC_PTHREAD   -std=c++17 -o CMakeFiles/cmTC_572b9.dir/src.cxx.o -c /home/<USER>/tbox/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_572b9
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_572b9.dir/link.txt --verbose=1
/usr/bin/c++   -Wall -Wextra -Wpedantic -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_572b9.dir/src.cxx.o  -o cmTC_572b9 
/usr/bin/ld: CMakeFiles/cmTC_572b9.dir/src.cxx.o: in function `main':
src.cxx:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.cxx:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.cxx:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_572b9.dir/build.make:87: cmTC_572b9] Error 1
make[1]: Leaving directory '/home/<USER>/tbox/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_572b9/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/tbox/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_7c69d/fast && /usr/bin/make -f CMakeFiles/cmTC_7c69d.dir/build.make CMakeFiles/cmTC_7c69d.dir/build
make[1]: Entering directory '/home/<USER>/tbox/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_7c69d.dir/CheckFunctionExists.cxx.o
/usr/bin/c++    -Wall -Wextra -Wpedantic -DCHECK_FUNCTION_EXISTS=pthread_create   -std=c++17 -o CMakeFiles/cmTC_7c69d.dir/CheckFunctionExists.cxx.o -c /home/<USER>/tbox/build/CMakeFiles/CheckLibraryExists/CheckFunctionExists.cxx
Linking CXX executable cmTC_7c69d
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7c69d.dir/link.txt --verbose=1
/usr/bin/c++   -Wall -Wextra -Wpedantic -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_7c69d.dir/CheckFunctionExists.cxx.o  -o cmTC_7c69d  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_7c69d.dir/build.make:87: cmTC_7c69d] Error 1
make[1]: Leaving directory '/home/<USER>/tbox/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_7c69d/fast] Error 2



