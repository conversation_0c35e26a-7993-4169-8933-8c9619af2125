#!/bin/bash

# GPIO电子锁控制脚本
# 控制 /tmp/GPIO_GUNA_ELOCK_CTRL_P 和 /tmp/GPIO_GUNA_ELOCK_CTRL_N
# 作者: tbox项目
# 日期: $(date +%Y-%m-%d)

# GPIO文件路径定义
GPIO_P_PATH="/tmp/GPIO_GUNA_ELOCK_CTRL_P"
GPIO_N_PATH="/tmp/GPIO_GUNA_ELOCK_CTRL_N"

# 日志文件路径
LOG_FILE="/tmp/elock_control.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"

    case $level in
        "ERROR")
            echo -e "${RED}[错误] $message${NC}" >&2
            ;;
        "WARN")
            echo -e "${YELLOW}[警告] $message${NC}"
            ;;
        "INFO")
            echo -e "${GREEN}[信息] $message${NC}"
            ;;
        "DEBUG")
            echo -e "${BLUE}[调试] $message${NC}"
            ;;
    esac
}

# 检查GPIO文件是否存在
check_gpio_files() {
    local missing_files=()

    if [ ! -e "$GPIO_P_PATH" ]; then
        missing_files+=("$GPIO_P_PATH")
    fi

    if [ ! -e "$GPIO_N_PATH" ]; then
        missing_files+=("$GPIO_N_PATH")
    fi

    if [ ${#missing_files[@]} -gt 0 ]; then
        log_message "ERROR" "GPIO文件不存在: ${missing_files[*]}"
        return 1
    fi

    return 0
}

# 初始化GPIO文件
init_gpio_files() {
    log_message "INFO" "正在初始化GPIO文件..."

    # 创建GPIO文件（如果不存在）
    if [ ! -e "$GPIO_P_PATH" ]; then
        touch "$GPIO_P_PATH"
        chmod 666 "$GPIO_P_PATH"
        log_message "INFO" "创建GPIO正极控制文件: $GPIO_P_PATH"
    fi

    if [ ! -e "$GPIO_N_PATH" ]; then
        touch "$GPIO_N_PATH"
        chmod 666 "$GPIO_N_PATH"
        log_message "INFO" "创建GPIO负极控制文件: $GPIO_N_PATH"
    fi

    # 初始化为关锁状态
    echo "0" > "$GPIO_P_PATH"
    echo "0" > "$GPIO_N_PATH"

    log_message "INFO" "GPIO文件初始化完成"
}

# 读取GPIO状态
read_gpio_status() {
    local gpio_p_value=""
    local gpio_n_value=""

    if [ -r "$GPIO_P_PATH" ]; then
        gpio_p_value=$(cat "$GPIO_P_PATH" 2>/dev/null | tr -d '\n\r ')
    else
        log_message "ERROR" "无法读取GPIO正极文件: $GPIO_P_PATH"
        return 1
    fi

    if [ -r "$GPIO_N_PATH" ]; then
        gpio_n_value=$(cat "$GPIO_N_PATH" 2>/dev/null | tr -d '\n\r ')
    else
        log_message "ERROR" "无法读取GPIO负极文件: $GPIO_N_PATH"
        return 1
    fi

    echo "GPIO_P:$gpio_p_value,GPIO_N:$gpio_n_value"
    return 0
}

# 设置GPIO值
set_gpio_value() {
    local p_value=$1
    local n_value=$2

    # 参数验证
    if [[ ! "$p_value" =~ ^[01]$ ]] || [[ ! "$n_value" =~ ^[01]$ ]]; then
        log_message "ERROR" "GPIO值必须是0或1"
        return 1
    fi

    # 写入GPIO值
    if ! echo "$p_value" > "$GPIO_P_PATH" 2>/dev/null; then
        log_message "ERROR" "写入GPIO正极文件失败: $GPIO_P_PATH"
        return 1
    fi

    if ! echo "$n_value" > "$GPIO_N_PATH" 2>/dev/null; then
        log_message "ERROR" "写入GPIO负极文件失败: $GPIO_N_PATH"
        return 1
    fi

    log_message "INFO" "GPIO设置成功: P=$p_value, N=$n_value"
    return 0
}

# 开锁操作
unlock_door() {
    local duration=${1:-3}  # 默认开锁3秒

    log_message "INFO" "开始开锁操作，持续时间: ${duration}秒"

    # 检查GPIO文件
    if ! check_gpio_files; then
        return 1
    fi

    # 设置开锁状态 (P=1, N=0)
    if ! set_gpio_value 1 0; then
        log_message "ERROR" "开锁失败：无法设置GPIO值"
        return 1
    fi

    log_message "INFO" "电子锁已开启"

    # 等待指定时间
    sleep "$duration"

    # 恢复关锁状态
    if ! set_gpio_value 0 0; then
        log_message "WARN" "警告：无法恢复关锁状态"
        return 1
    fi

    log_message "INFO" "电子锁已自动关闭"
    return 0
}

# 关锁操作
lock_door() {
    log_message "INFO" "开始关锁操作"

    # 检查GPIO文件
    if ! check_gpio_files; then
        return 1
    fi

    # 设置关锁状态 (P=0, N=0)
    if ! set_gpio_value 0 0; then
        log_message "ERROR" "关锁失败：无法设置GPIO值"
        return 1
    fi

    log_message "INFO" "电子锁已关闭"
    return 0
}

# 查询锁状态
query_status() {
    log_message "INFO" "查询电子锁状态"

    if ! check_gpio_files; then
        return 1
    fi

    local status=$(read_gpio_status)
    if [ $? -ne 0 ]; then
        return 1
    fi

    echo "当前GPIO状态: $status"

    # 解析状态
    local p_val=$(echo "$status" | cut -d',' -f1 | cut -d':' -f2)
    local n_val=$(echo "$status" | cut -d',' -f2 | cut -d':' -f2)

    if [ "$p_val" = "1" ] && [ "$n_val" = "0" ]; then
        echo -e "${GREEN}电子锁状态: 开启${NC}"
    elif [ "$p_val" = "0" ] && [ "$n_val" = "0" ]; then
        echo -e "${RED}电子锁状态: 关闭${NC}"
    else
        echo -e "${YELLOW}电子锁状态: 异常 (P=$p_val, N=$n_val)${NC}"
        log_message "WARN" "检测到异常的GPIO状态: P=$p_val, N=$n_val"
    fi

    return 0
}

# 重置GPIO
reset_gpio() {
    log_message "INFO" "重置GPIO状态"

    if ! set_gpio_value 0 0; then
        log_message "ERROR" "GPIO重置失败"
        return 1
    fi

    log_message "INFO" "GPIO重置完成"
    return 0
}

# 显示帮助信息
show_help() {
    cat << EOF
GPIO电子锁控制脚本

用法: $0 [选项] [参数]

选项:
    init                    初始化GPIO文件
    unlock [秒数]          开锁（默认3秒后自动关锁）
    lock                   关锁
    status                 查询锁状态
    reset                  重置GPIO状态
    help                   显示此帮助信息

示例:
    $0 init                # 初始化GPIO文件
    $0 unlock              # 开锁3秒
    $0 unlock 5            # 开锁5秒
    $0 lock                # 关锁
    $0 status              # 查询状态
    $0 reset               # 重置GPIO

GPIO文件路径:
    正极控制: $GPIO_P_PATH
    负极控制: $GPIO_N_PATH
    日志文件: $LOG_FILE

EOF
}

# 主函数
main() {
    # 创建日志文件目录
    mkdir -p "$(dirname "$LOG_FILE")"

    case "${1:-help}" in
        "init")
            init_gpio_files
            ;;
        "unlock")
            unlock_door "${2:-3}"
            ;;
        "lock")
            lock_door
            ;;
        "status")
            query_status
            ;;
        "reset")
            reset_gpio
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知选项 '$1'${NC}" >&2
            echo "使用 '$0 help' 查看帮助信息" >&2
            exit 1
            ;;
    esac
}

# 脚本入口点
main "$@"