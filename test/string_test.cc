#include "util/string.h"

#include <gtest/gtest.h>


TEST(string, Split) {
    std::vector<std::string> str_vec;
    // ASSERT_EQ判断两个参数是否相同 若不同直接中止
    ASSERT_EQ(util::string::Split("A::BB:C::DD ", "::", str_vec), 3u);
    EXPECT_EQ(str_vec[0], std::string("A"));
    EXPECT_EQ(str_vec[1], std::string("BB:C"));
    EXPECT_EQ(str_vec[2], std::string("DD "));

    str_vec.clear();
    ASSERT_EQ(util::string::Split("A::BB:C::DD ", ":::", str_vec), 1u);
    EXPECT_EQ(str_vec[0], std::string("A::BB:C::DD "));

    str_vec.clear();
    ASSERT_EQ(util::string::Split("A::BB:C::DD ", ":", str_vec), 6u);
    EXPECT_EQ(str_vec[0], std::string("A"));
    EXPECT_EQ(str_vec[1], std::string(""));
    EXPECT_EQ(str_vec[2], std::string("BB"));
    EXPECT_EQ(str_vec[3], std::string("C"));
    EXPECT_EQ(str_vec[4], std::string(""));
    EXPECT_EQ(str_vec[5], std::string("DD "));
}