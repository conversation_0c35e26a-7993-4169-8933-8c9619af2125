# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

# Utility rule file for run_tests.

# Include the progress variables for this target.
include test/CMakeFiles/run_tests.dir/progress.make

test/CMakeFiles/run_tests: ../test/fd_test.cc
test/CMakeFiles/run_tests: ../test/fs_test.cc
test/CMakeFiles/run_tests: ../test/string_test.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running all tests via CTest"
	/usr/bin/ctest --output-on-failure --verbose

run_tests: test/CMakeFiles/run_tests
run_tests: test/CMakeFiles/run_tests.dir/build.make

.PHONY : run_tests

# Rule to build all files generated by this target.
test/CMakeFiles/run_tests.dir/build: run_tests

.PHONY : test/CMakeFiles/run_tests.dir/build

test/CMakeFiles/run_tests.dir/clean:
	cd /home/<USER>/tbox/build/test && $(CMAKE_COMMAND) -P CMakeFiles/run_tests.dir/cmake_clean.cmake
.PHONY : test/CMakeFiles/run_tests.dir/clean

test/CMakeFiles/run_tests.dir/depend:
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/tbox /home/<USER>/tbox/test /home/<USER>/tbox/build /home/<USER>/tbox/build/test /home/<USER>/tbox/build/test/CMakeFiles/run_tests.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : test/CMakeFiles/run_tests.dir/depend

