# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

# Include any dependencies generated for this target.
include CMakeFiles/tbox_event.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/tbox_event.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/tbox_event.dir/flags.make

CMakeFiles/tbox_event.dir/event/common_loop.cpp.o: CMakeFiles/tbox_event.dir/flags.make
CMakeFiles/tbox_event.dir/event/common_loop.cpp.o: ../event/common_loop.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/tbox_event.dir/event/common_loop.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_event.dir/event/common_loop.cpp.o -c /home/<USER>/tbox/event/common_loop.cpp

CMakeFiles/tbox_event.dir/event/common_loop.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_event.dir/event/common_loop.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/event/common_loop.cpp > CMakeFiles/tbox_event.dir/event/common_loop.cpp.i

CMakeFiles/tbox_event.dir/event/common_loop.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_event.dir/event/common_loop.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/event/common_loop.cpp -o CMakeFiles/tbox_event.dir/event/common_loop.cpp.s

CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.o: CMakeFiles/tbox_event.dir/flags.make
CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.o: ../event/epoll_fd_event.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.o -c /home/<USER>/tbox/event/epoll_fd_event.cpp

CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/event/epoll_fd_event.cpp > CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.i

CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/event/epoll_fd_event.cpp -o CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.s

CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.o: CMakeFiles/tbox_event.dir/flags.make
CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.o: ../event/epoll_loop.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.o -c /home/<USER>/tbox/event/epoll_loop.cpp

CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/event/epoll_loop.cpp > CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.i

CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/event/epoll_loop.cpp -o CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.s

CMakeFiles/tbox_event.dir/event/loop.cpp.o: CMakeFiles/tbox_event.dir/flags.make
CMakeFiles/tbox_event.dir/event/loop.cpp.o: ../event/loop.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/tbox_event.dir/event/loop.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_event.dir/event/loop.cpp.o -c /home/<USER>/tbox/event/loop.cpp

CMakeFiles/tbox_event.dir/event/loop.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_event.dir/event/loop.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/event/loop.cpp > CMakeFiles/tbox_event.dir/event/loop.cpp.i

CMakeFiles/tbox_event.dir/event/loop.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_event.dir/event/loop.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/event/loop.cpp -o CMakeFiles/tbox_event.dir/event/loop.cpp.s

CMakeFiles/tbox_event.dir/event/stat.cpp.o: CMakeFiles/tbox_event.dir/flags.make
CMakeFiles/tbox_event.dir/event/stat.cpp.o: ../event/stat.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/tbox_event.dir/event/stat.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_event.dir/event/stat.cpp.o -c /home/<USER>/tbox/event/stat.cpp

CMakeFiles/tbox_event.dir/event/stat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_event.dir/event/stat.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/event/stat.cpp > CMakeFiles/tbox_event.dir/event/stat.cpp.i

CMakeFiles/tbox_event.dir/event/stat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_event.dir/event/stat.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/event/stat.cpp -o CMakeFiles/tbox_event.dir/event/stat.cpp.s

# Object files for target tbox_event
tbox_event_OBJECTS = \
"CMakeFiles/tbox_event.dir/event/common_loop.cpp.o" \
"CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.o" \
"CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.o" \
"CMakeFiles/tbox_event.dir/event/loop.cpp.o" \
"CMakeFiles/tbox_event.dir/event/stat.cpp.o"

# External object files for target tbox_event
tbox_event_EXTERNAL_OBJECTS =

lib/libtbox_event.a: CMakeFiles/tbox_event.dir/event/common_loop.cpp.o
lib/libtbox_event.a: CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.o
lib/libtbox_event.a: CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.o
lib/libtbox_event.a: CMakeFiles/tbox_event.dir/event/loop.cpp.o
lib/libtbox_event.a: CMakeFiles/tbox_event.dir/event/stat.cpp.o
lib/libtbox_event.a: CMakeFiles/tbox_event.dir/build.make
lib/libtbox_event.a: CMakeFiles/tbox_event.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX static library lib/libtbox_event.a"
	$(CMAKE_COMMAND) -P CMakeFiles/tbox_event.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tbox_event.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/tbox_event.dir/build: lib/libtbox_event.a

.PHONY : CMakeFiles/tbox_event.dir/build

CMakeFiles/tbox_event.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/tbox_event.dir/cmake_clean.cmake
.PHONY : CMakeFiles/tbox_event.dir/clean

CMakeFiles/tbox_event.dir/depend:
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/tbox /home/<USER>/tbox /home/<USER>/tbox/build /home/<USER>/tbox/build /home/<USER>/tbox/build/CMakeFiles/tbox_event.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/tbox_event.dir/depend

