# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

# Include any dependencies generated for this target.
include test/CMakeFiles/string_test.dir/depend.make

# Include the progress variables for this target.
include test/CMakeFiles/string_test.dir/progress.make

# Include the compile flags for this target's objects.
include test/CMakeFiles/string_test.dir/flags.make

test/CMakeFiles/string_test.dir/string_test.cc.o: test/CMakeFiles/string_test.dir/flags.make
test/CMakeFiles/string_test.dir/string_test.cc.o: ../test/string_test.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object test/CMakeFiles/string_test.dir/string_test.cc.o"
	cd /home/<USER>/tbox/build/test && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/string_test.dir/string_test.cc.o -c /home/<USER>/tbox/test/string_test.cc

test/CMakeFiles/string_test.dir/string_test.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/string_test.dir/string_test.cc.i"
	cd /home/<USER>/tbox/build/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/test/string_test.cc > CMakeFiles/string_test.dir/string_test.cc.i

test/CMakeFiles/string_test.dir/string_test.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/string_test.dir/string_test.cc.s"
	cd /home/<USER>/tbox/build/test && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/test/string_test.cc -o CMakeFiles/string_test.dir/string_test.cc.s

# Object files for target string_test
string_test_OBJECTS = \
"CMakeFiles/string_test.dir/string_test.cc.o"

# External object files for target string_test
string_test_EXTERNAL_OBJECTS =

bin/tests/string_test: test/CMakeFiles/string_test.dir/string_test.cc.o
bin/tests/string_test: test/CMakeFiles/string_test.dir/build.make
bin/tests/string_test: /usr/lib/x86_64-linux-gnu/libgtest.a
bin/tests/string_test: /usr/lib/x86_64-linux-gnu/libgtest_main.a
bin/tests/string_test: lib/libtbox_util.a
bin/tests/string_test: /usr/lib/x86_64-linux-gnu/libgtest.a
bin/tests/string_test: test/CMakeFiles/string_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ../bin/tests/string_test"
	cd /home/<USER>/tbox/build/test && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/string_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
test/CMakeFiles/string_test.dir/build: bin/tests/string_test

.PHONY : test/CMakeFiles/string_test.dir/build

test/CMakeFiles/string_test.dir/clean:
	cd /home/<USER>/tbox/build/test && $(CMAKE_COMMAND) -P CMakeFiles/string_test.dir/cmake_clean.cmake
.PHONY : test/CMakeFiles/string_test.dir/clean

test/CMakeFiles/string_test.dir/depend:
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/tbox /home/<USER>/tbox/test /home/<USER>/tbox/build /home/<USER>/tbox/build/test /home/<USER>/tbox/build/test/CMakeFiles/string_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : test/CMakeFiles/string_test.dir/depend

