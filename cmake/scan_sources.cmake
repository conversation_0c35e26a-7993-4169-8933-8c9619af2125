# 源文件扫描脚本
# 该脚本在每次构建时运行，自动发现新的源文件

# 检查必需的变量
if(NOT DEFINED LIB_DIR)
    message(FATAL_ERROR "LIB_DIR must be defined")
endif()

if(NOT DEFINED OUTPUT_FILE)
    message(FATAL_ERROR "OUTPUT_FILE must be defined")
endif()

# 获取库名称（从输出文件路径推导）
get_filename_component(OUTPUT_DIR ${OUTPUT_FILE} DIRECTORY)
get_filename_component(OUTPUT_NAME ${OUTPUT_FILE} NAME_WE)
string(REGEX REPLACE "_sources$" "" LIB_NAME ${OUTPUT_NAME})

# 扫描源文件
file(GLOB_RECURSE SOURCES
    "${LIB_DIR}/*.cc"
    "${LIB_DIR}/*.cpp"
    "${LIB_DIR}/*.cxx"
    "${LIB_DIR}/*.c++"
)

# 扫描头文件
file(GLOB_RECURSE HEADERS
    "${LIB_DIR}/*.h"
    "${LIB_DIR}/*.hpp"
    "${LIB_DIR}/*.hxx"
    "${LIB_DIR}/*.h++"
)

# 生成CMake变量定义
set(OUTPUT_CONTENT "# 自动生成的源文件列表\n")
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 生成时间: ${CMAKE_CURRENT_LIST_FILE}\n")
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 扫描目录: ${LIB_DIR}\n\n")

# 写入源文件列表
if(SOURCES)
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}set(${LIB_NAME}_SOURCES\n")
    foreach(src ${SOURCES})
        # 使用相对路径
        file(RELATIVE_PATH rel_src ${CMAKE_SOURCE_DIR} ${src})
        set(OUTPUT_CONTENT "${OUTPUT_CONTENT}    \"${rel_src}\"\n")
    endforeach()
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT})\n\n")
else()
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}set(${LIB_NAME}_SOURCES)\n\n")
endif()

# 写入头文件列表
if(HEADERS)
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}set(${LIB_NAME}_HEADERS\n")
    foreach(hdr ${HEADERS})
        # 使用相对路径
        file(RELATIVE_PATH rel_hdr ${CMAKE_SOURCE_DIR} ${hdr})
        set(OUTPUT_CONTENT "${OUTPUT_CONTENT}    \"${rel_hdr}\"\n")
    endforeach()
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT})\n\n")
else()
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}set(${LIB_NAME}_HEADERS)\n\n")
endif()

# 添加调试信息
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 调试信息\n")
list(LENGTH SOURCES SOURCES_COUNT)
list(LENGTH HEADERS HEADERS_COUNT)
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 找到 ${SOURCES_COUNT} 个源文件，${HEADERS_COUNT} 个头文件\n")

if(SOURCES)
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 源文件:\n")
    foreach(src ${SOURCES})
        get_filename_component(src_name ${src} NAME)
        set(OUTPUT_CONTENT "${OUTPUT_CONTENT}#   - ${src_name}\n")
    endforeach()
endif()

# 检查输出文件是否需要更新
set(NEED_UPDATE TRUE)
if(EXISTS ${OUTPUT_FILE})
    file(READ ${OUTPUT_FILE} EXISTING_CONTENT)
    if("${EXISTING_CONTENT}" STREQUAL "${OUTPUT_CONTENT}")
        set(NEED_UPDATE FALSE)
    endif()
endif()

# 强制更新检查：如果这是make构建过程中的调用，总是检查文件变化
if(DEFINED ENV{MAKEFLAGS})
    set(NEED_UPDATE TRUE)
endif()

# 只有在内容发生变化时才写入文件
if(NEED_UPDATE)
    file(WRITE ${OUTPUT_FILE} ${OUTPUT_CONTENT})
    message(STATUS "Updated source list for ${LIB_NAME}: ${SOURCES_COUNT} sources, ${HEADERS_COUNT} headers")

    # 如果发现新文件，输出详细信息
    if(SOURCES)
        message(STATUS "Source files in ${LIB_DIR}:")
        foreach(src ${SOURCES})
            get_filename_component(src_name ${src} NAME)
            message(STATUS "  - ${src_name}")
        endforeach()
    endif()
else()
    message(STATUS "Source list for ${LIB_NAME} is up to date")
endif()
