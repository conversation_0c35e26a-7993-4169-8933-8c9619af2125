Start testing: Jun 30 09:44 HKT
----------------------------------------------------------
1/2 Testing: fd_test
1/2 Test: fd_test
Command: "/home/<USER>/tbox/build/bin/tests/fd_test"
Directory: /home/<USER>/tbox/build/bin/tests
"fd_test" start time: Jun 30 09:44 HKT
Output:
----------------------------------------------------------
Running main() from /build/googletest-j5yxiC/googletest-1.10.0/googletest/src/gtest_main.cc
[0;32m[==========] [mRunning 15 tests from 1 test suite.
[0;32m[----------] [mGlobal test environment set-up.
[0;32m[----------] [m15 tests from Fd
[0;32m[ RUN      ] [mFd.close_func
[0;32m[       OK ] [mFd.close_func (0 ms)
[0;32m[ RUN      ] [mFd.swap
[0;32m[       OK ] [mFd.swap (0 ms)
[0;32m[ RUN      ] [mFd.move_1
[0;32m[       OK ] [mFd.move_1 (0 ms)
[0;32m[ RUN      ] [mFd.move_2
[0;32m[       OK ] [mFd.move_2 (0 ms)
[0;32m[ RUN      ] [mFd.reset
[0;32m[       OK ] [mFd.reset (0 ms)
[0;32m[ RUN      ] [mFd.close
[0;32m[       OK ] [mFd.close (0 ms)
[0;32m[ RUN      ] [mFd.cast
[0;32m[       OK ] [mFd.cast (0 ms)
[0;32m[ RUN      ] [mFd.copy_construct
[0;32m[       OK ] [mFd.copy_construct (0 ms)
[0;32m[ RUN      ] [mFd.copy_assign_no_value
[0;32m[       OK ] [mFd.copy_assign_no_value (0 ms)
[0;32m[ RUN      ] [mFd.copy_assign_has_value
[0;32m[       OK ] [mFd.copy_assign_has_value (0 ms)
[0;32m[ RUN      ] [mFd.move_construct
[0;32m[       OK ] [mFd.move_construct (0 ms)
[0;32m[ RUN      ] [mFd.move_assign_1
[0;32m[       OK ] [mFd.move_assign_1 (0 ms)
[0;32m[ RUN      ] [mFd.move_assign_2
[0;32m[       OK ] [mFd.move_assign_2 (0 ms)
[0;32m[ RUN      ] [mFd.move_assign_3
[0;32m[       OK ] [mFd.move_assign_3 (0 ms)
[0;32m[ RUN      ] [mFd.move_assign_4
[0;32m[       OK ] [mFd.move_assign_4 (0 ms)
[0;32m[----------] [m15 tests from Fd (0 ms total)

[0;32m[----------] [mGlobal test environment tear-down
[0;32m[==========] [m15 tests from 1 test suite ran. (0 ms total)
[0;32m[  PASSED  ] [m15 tests.
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"fd_test" end time: Jun 30 09:44 HKT
"fd_test" time elapsed: 00:00:00
----------------------------------------------------------

2/2 Testing: fs_test
2/2 Test: fs_test
Command: "/home/<USER>/tbox/build/bin/tests/fs_test"
Directory: /home/<USER>/tbox/build/bin/tests
"fs_test" start time: Jun 30 09:44 HKT
Output:
----------------------------------------------------------
Running main() from /build/googletest-j5yxiC/googletest-1.10.0/googletest/src/gtest_main.cc
[0;32m[==========] [mRunning 1 test from 1 test suite.
[0;32m[----------] [mGlobal test environment set-up.
[0;32m[----------] [m1 test from Fs
[0;32m[ RUN      ] [mFs.IsDirectoryExist
[0;32m[       OK ] [mFs.IsDirectoryExist (0 ms)
[0;32m[----------] [m1 test from Fs (0 ms total)

[0;32m[----------] [mGlobal test environment tear-down
[0;32m[==========] [m1 test from 1 test suite ran. (0 ms total)
[0;32m[  PASSED  ] [m1 test.
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"fs_test" end time: Jun 30 09:44 HKT
"fs_test" time elapsed: 00:00:00
----------------------------------------------------------

End testing: Jun 30 09:44 HKT
