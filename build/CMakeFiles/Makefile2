# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/tbox_event.dir/all
all: CMakeFiles/tbox_util.dir/all
all: test/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall: test/preinstall

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/tbox_event.dir/clean
clean: CMakeFiles/tbox_util.dir/clean
clean: test/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory test

# Recursive "all" directory target.
test/all: test/CMakeFiles/fd_test.dir/all
test/all: test/CMakeFiles/fs_test.dir/all
test/all: test/CMakeFiles/string_test.dir/all

.PHONY : test/all

# Recursive "preinstall" directory target.
test/preinstall:

.PHONY : test/preinstall

# Recursive "clean" directory target.
test/clean: test/CMakeFiles/fd_test.dir/clean
test/clean: test/CMakeFiles/fs_test.dir/clean
test/clean: test/CMakeFiles/run_tests_direct.dir/clean
test/clean: test/CMakeFiles/string_test.dir/clean
test/clean: test/CMakeFiles/clean_tests.dir/clean
test/clean: test/CMakeFiles/test_help.dir/clean
test/clean: test/CMakeFiles/run_tests.dir/clean

.PHONY : test/clean

#=============================================================================
# Target rules for target CMakeFiles/tbox_event.dir

# All Build rule for target.
CMakeFiles/tbox_event.dir/all:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/depend
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=9,10,11,12,13,14 "Built target tbox_event"
.PHONY : CMakeFiles/tbox_event.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tbox_event.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 6
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/tbox_event.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : CMakeFiles/tbox_event.dir/rule

# Convenience name for target.
tbox_event: CMakeFiles/tbox_event.dir/rule

.PHONY : tbox_event

# clean rule for target.
CMakeFiles/tbox_event.dir/clean:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/clean
.PHONY : CMakeFiles/tbox_event.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/tbox_util.dir

# All Build rule for target.
CMakeFiles/tbox_util.dir/all:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/depend
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=15,16,17,18,19 "Built target tbox_util"
.PHONY : CMakeFiles/tbox_util.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tbox_util.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 5
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/tbox_util.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : CMakeFiles/tbox_util.dir/rule

# Convenience name for target.
tbox_util: CMakeFiles/tbox_util.dir/rule

.PHONY : tbox_util

# clean rule for target.
CMakeFiles/tbox_util.dir/clean:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/clean
.PHONY : CMakeFiles/tbox_util.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/fd_test.dir

# All Build rule for target.
test/CMakeFiles/fd_test.dir/all: CMakeFiles/tbox_util.dir/all
	$(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/depend
	$(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=2,3 "Built target fd_test"
.PHONY : test/CMakeFiles/fd_test.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/fd_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 7
	$(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/fd_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : test/CMakeFiles/fd_test.dir/rule

# Convenience name for target.
fd_test: test/CMakeFiles/fd_test.dir/rule

.PHONY : fd_test

# clean rule for target.
test/CMakeFiles/fd_test.dir/clean:
	$(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/clean
.PHONY : test/CMakeFiles/fd_test.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/fs_test.dir

# All Build rule for target.
test/CMakeFiles/fs_test.dir/all: CMakeFiles/tbox_util.dir/all
	$(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/depend
	$(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=4,5 "Built target fs_test"
.PHONY : test/CMakeFiles/fs_test.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/fs_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 7
	$(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/fs_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : test/CMakeFiles/fs_test.dir/rule

# Convenience name for target.
fs_test: test/CMakeFiles/fs_test.dir/rule

.PHONY : fs_test

# clean rule for target.
test/CMakeFiles/fs_test.dir/clean:
	$(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/clean
.PHONY : test/CMakeFiles/fs_test.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/run_tests_direct.dir

# All Build rule for target.
test/CMakeFiles/run_tests_direct.dir/all:
	$(MAKE) -f test/CMakeFiles/run_tests_direct.dir/build.make test/CMakeFiles/run_tests_direct.dir/depend
	$(MAKE) -f test/CMakeFiles/run_tests_direct.dir/build.make test/CMakeFiles/run_tests_direct.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num= "Built target run_tests_direct"
.PHONY : test/CMakeFiles/run_tests_direct.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/run_tests_direct.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/run_tests_direct.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : test/CMakeFiles/run_tests_direct.dir/rule

# Convenience name for target.
run_tests_direct: test/CMakeFiles/run_tests_direct.dir/rule

.PHONY : run_tests_direct

# clean rule for target.
test/CMakeFiles/run_tests_direct.dir/clean:
	$(MAKE) -f test/CMakeFiles/run_tests_direct.dir/build.make test/CMakeFiles/run_tests_direct.dir/clean
.PHONY : test/CMakeFiles/run_tests_direct.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/string_test.dir

# All Build rule for target.
test/CMakeFiles/string_test.dir/all: CMakeFiles/tbox_util.dir/all
	$(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/depend
	$(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=7,8 "Built target string_test"
.PHONY : test/CMakeFiles/string_test.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/string_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 7
	$(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/string_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : test/CMakeFiles/string_test.dir/rule

# Convenience name for target.
string_test: test/CMakeFiles/string_test.dir/rule

.PHONY : string_test

# clean rule for target.
test/CMakeFiles/string_test.dir/clean:
	$(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/clean
.PHONY : test/CMakeFiles/string_test.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/clean_tests.dir

# All Build rule for target.
test/CMakeFiles/clean_tests.dir/all:
	$(MAKE) -f test/CMakeFiles/clean_tests.dir/build.make test/CMakeFiles/clean_tests.dir/depend
	$(MAKE) -f test/CMakeFiles/clean_tests.dir/build.make test/CMakeFiles/clean_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=1 "Built target clean_tests"
.PHONY : test/CMakeFiles/clean_tests.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/clean_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/clean_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : test/CMakeFiles/clean_tests.dir/rule

# Convenience name for target.
clean_tests: test/CMakeFiles/clean_tests.dir/rule

.PHONY : clean_tests

# clean rule for target.
test/CMakeFiles/clean_tests.dir/clean:
	$(MAKE) -f test/CMakeFiles/clean_tests.dir/build.make test/CMakeFiles/clean_tests.dir/clean
.PHONY : test/CMakeFiles/clean_tests.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_help.dir

# All Build rule for target.
test/CMakeFiles/test_help.dir/all:
	$(MAKE) -f test/CMakeFiles/test_help.dir/build.make test/CMakeFiles/test_help.dir/depend
	$(MAKE) -f test/CMakeFiles/test_help.dir/build.make test/CMakeFiles/test_help.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num= "Built target test_help"
.PHONY : test/CMakeFiles/test_help.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_help.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/test_help.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : test/CMakeFiles/test_help.dir/rule

# Convenience name for target.
test_help: test/CMakeFiles/test_help.dir/rule

.PHONY : test_help

# clean rule for target.
test/CMakeFiles/test_help.dir/clean:
	$(MAKE) -f test/CMakeFiles/test_help.dir/build.make test/CMakeFiles/test_help.dir/clean
.PHONY : test/CMakeFiles/test_help.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/run_tests.dir

# All Build rule for target.
test/CMakeFiles/run_tests.dir/all:
	$(MAKE) -f test/CMakeFiles/run_tests.dir/build.make test/CMakeFiles/run_tests.dir/depend
	$(MAKE) -f test/CMakeFiles/run_tests.dir/build.make test/CMakeFiles/run_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=6 "Built target run_tests"
.PHONY : test/CMakeFiles/run_tests.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/run_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/run_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : test/CMakeFiles/run_tests.dir/rule

# Convenience name for target.
run_tests: test/CMakeFiles/run_tests.dir/rule

.PHONY : run_tests

# clean rule for target.
test/CMakeFiles/run_tests.dir/clean:
	$(MAKE) -f test/CMakeFiles/run_tests.dir/build.make test/CMakeFiles/run_tests.dir/clean
.PHONY : test/CMakeFiles/run_tests.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -P /home/<USER>/tbox/build/CMakeFiles/VerifyGlobs.cmake
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

