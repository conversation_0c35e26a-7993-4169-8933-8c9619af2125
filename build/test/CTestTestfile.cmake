# CMake generated Testfile for 
# Source directory: /home/<USER>/tbox/test
# Build directory: /home/<USER>/tbox/build/test
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(fd_test "/home/<USER>/tbox/build/bin/tests/fd_test")
set_tests_properties(fd_test PROPERTIES  ENVIRONMENT "GTEST_COLOR=yes" TIMEOUT "30" WORKING_DIRECTORY "/home/<USER>/tbox/build/bin/tests" _BACKTRACE_TRIPLES "/home/<USER>/tbox/test/CMakeLists.txt;81;add_test;/home/<USER>/tbox/test/CMakeLists.txt;0;")
add_test(fs_test "/home/<USER>/tbox/build/bin/tests/fs_test")
set_tests_properties(fs_test PROPERTIES  ENVIRONMENT "GTEST_COLOR=yes" TIMEOUT "30" WORKING_DIRECTORY "/home/<USER>/tbox/build/bin/tests" _BACKTRACE_TRIPLES "/home/<USER>/tbox/test/CMakeLists.txt;81;add_test;/home/<USER>/tbox/test/CMakeLists.txt;0;")
add_test(string_test "/home/<USER>/tbox/build/bin/tests/string_test")
set_tests_properties(string_test PROPERTIES  ENVIRONMENT "GTEST_COLOR=yes" TIMEOUT "30" WORKING_DIRECTORY "/home/<USER>/tbox/build/bin/tests" _BACKTRACE_TRIPLES "/home/<USER>/tbox/test/CMakeLists.txt;81;add_test;/home/<USER>/tbox/test/CMakeLists.txt;0;")
