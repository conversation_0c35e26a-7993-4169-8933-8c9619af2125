#ifndef EVENT_ITEM_H_
#define EVENT_ITEM_H_

#include <string>

namespace tbox {
namespace event {

class Loop;

class Event {
  public:
    Event(const std::string &what) : what_(what) { }

    enum class Mode {
        kPersist,
        kOneshot
    };

    virtual bool isEnabled() const = 0;
    virtual bool enable() = 0;
    virtual bool disable() = 0;

    virtual Loop* getLoop() const = 0;

    std::string what() const { return what_; }

  public:
    virtual ~Event() { }

  protected:
    std::string what_;
};

}
}

#endif //EVENT_ITEM_H_
