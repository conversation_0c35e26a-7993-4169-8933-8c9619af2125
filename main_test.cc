#include <iostream>
#include <fstream>
#include <string>
#include <stdexcept>

bool ReadFileContent(const std::string &filename, std::string &content) {
    try {
        std::ifstream file(filename);
        if (!file) {
            // 抛出异常：文件无法打开
            throw std::runtime_error("无法打开文件: " + filename);
        }

        // 读取文件内容到字符串
        content = std::string((std::istreambuf_iterator<char>(file)),
                              std::istreambuf_iterator<char>());
        return true;
    } catch (const std::ios_base::failure &e) {
        std::cerr << "IO 异常: " << e.what() << std::endl;
    } catch (const std::runtime_error &e) {
        std::cerr << "运行时错误: " << e.what() << std::endl;
    } catch (const std::exception &e) {
        std::cerr << "标准异常: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "未知异常发生" << std::endl;
    }

    return false;
}

int main() {
    std::string content;
    std::string filename = "example.txt";  // 你可以尝试修改为一个不存在的文件名测试异常

    if (ReadFileContent(filename, content)) {
        std::cout << "文件内容:\n" << content << std::endl;
    } else {
        std::cout << "文件读取失败。" << std::endl;
    }

    return 0;
}