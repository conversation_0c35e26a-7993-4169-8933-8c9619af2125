# tbox CMake 构建系统

这是一个为 tbox 项目设计的具有扩展性的 CMake 构建系统，特别针对测试文件的管理进行了优化。

## 特性

### 🚀 自动化测试发现

- 自动发现 `test/` 目录下所有 `*_test.cc` 和 `*_test.cpp` 文件
- 为每个测试文件自动创建独立的可执行文件
- 无需手动修改 CMake 配置即可添加新测试

### 📦 模块化设计

- 自动构建 `util` 和 `base` 库
- 支持静态库和动态库
- 清晰的目录结构和依赖关系

### 🔧 灵活配置

- 支持 Debug 和 Release 构建模式
- 自动检测和配置 GTest
- 跨平台兼容性

## 快速开始

### 1. 安装依赖

#### Ubuntu/Debian

```bash
sudo apt-get update
sudo apt-get install cmake build-essential libgtest-dev
```

#### CentOS/RHEL

```bash
sudo yum install cmake gcc-c++ gtest-devel
```

#### macOS

```bash
brew install cmake googletest
```

### 2. 构建项目

```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake ..

# 构建项目
make

# 运行测试
make test
```

### 3. 高级用法

```bash
# 查看测试帮助
make test_help

# 运行详细测试输出
make run_tests

# 只构建特定测试
make fd_test

# 运行特定测试
./bin/tests/fd_test

# 清理测试二进制文件
make clean_tests
```

## 目录结构

```base
tbox/
├── CMakeLists.txt          # 主CMake配置
├── test/
│   ├── CMakeLists.txt      # 测试CMake配置（自动发现测试）
│   ├── fd_test.cc          # 现有测试文件
│   └── *_test.cc           # 未来的测试文件（自动发现）
├── util/
│   ├── fd.h
│   ├── fd.cc
│   └── ...                 # 其他util文件
├── base/
│   ├── defines.h
│   └── ...                 # 其他base文件
└── build/                  # 构建输出目录
    ├── bin/
    │   └── tests/          # 测试可执行文件
    └── lib/                # 库文件
```

## 添加新测试

要添加新的测试文件，只需：

1. 在 `test/` 目录下创建新的 `*_test.cc` 文件
2. 重新运行 `make`

例如：

```bash
# 添加新测试文件
touch test/string_test.cc

# 重新构建（会自动发现新测试）
cd build && make

# 新测试会自动可用
./bin/tests/string_test
```

## CMake 选项

### 构建选项

- `BUILD_TESTS`: 是否构建测试（默认: ON）
- `CMAKE_BUILD_TYPE`: 构建类型（Debug/Release）

### 使用示例

```bash
# 禁用测试构建
cmake -DBUILD_TESTS=OFF ..

# Release 构建
cmake -DCMAKE_BUILD_TYPE=Release ..

# 自定义安装路径
cmake -DCMAKE_INSTALL_PREFIX=/usr/local ..
```

## 测试框架集成

该构建系统支持：

- **GTest**: Google Test 框架（推荐）
- **自动链接**: 自动链接 gtest 和 gtest_main
- **CTest 集成**: 与 CMake 的 CTest 完全集成

## 故障排除

### GTest 未找到

如果遇到 GTest 相关错误：

```bash
# 检查 GTest 安装
pkg-config --cflags --libs gtest

# 手动指定 GTest 路径
cmake -DGTest_ROOT=/path/to/gtest ..
```

### 编译错误

确保 C++17 支持：

```bash
# 检查编译器版本
g++ --version  # 需要 GCC 7+ 或 Clang 5+
```

## 扩展性设计

### 自动库发现

- 自动扫描 `util/` 和 `base/` 目录下的源文件
- 自动创建对应的静态库
- 测试自动链接相关库

### 模块化添加

要添加新模块（如 `network/`）：

1. 创建目录和源文件
2. 在主 `CMakeLists.txt` 中添加类似的库创建逻辑

### 测试扩展

测试系统支持：

测试系统支持：

- 多种测试文件命名模式
- 自定义测试配置
- 并行测试执行
- 测试超时控制

## 最佳实践

1. **测试文件命名**: 使用 `*_test.cc` 模式
2. **模块组织**: 将相关功能放在同一目录下
3. **依赖管理**: 利用 CMake 的自动依赖解析
4. **构建隔离**: 始终使用独立的 build 目录

## 贡献

要为项目添加新功能：

1. 在相应目录添加源文件
2. 添加对应的测试文件
3. CMake 会自动处理构建配置

这个构建系统设计为"零配置"添加新测试，让开发者专注于代码而不是构建配置。
