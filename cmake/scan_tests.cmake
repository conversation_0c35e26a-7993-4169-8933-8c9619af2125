# 测试文件扫描脚本
# 该脚本在每次构建时运行，自动发现新的测试文件

# 检查必需的变量
if(NOT DEFINED TEST_DIR)
    message(FATAL_ERROR "TEST_DIR must be defined")
endif()

if(NOT DEFINED OUTPUT_FILE)
    message(FATAL_ERROR "OUTPUT_FILE must be defined")
endif()

# 扫描测试文件
file(GLOB_RECURSE TEST_SOURCES 
    "${TEST_DIR}/*_test.cc" 
    "${TEST_DIR}/*_test.cpp"
    "${TEST_DIR}/*_test.cxx"
    "${TEST_DIR}/*_test.c++"
    "${TEST_DIR}/test_*.cc"
    "${TEST_DIR}/test_*.cpp"
    "${TEST_DIR}/test_*.cxx"
    "${TEST_DIR}/test_*.c++"
)

# 生成CMake变量定义
set(OUTPUT_CONTENT "# 自动生成的测试文件列表\n")
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 生成时间: ${CMAKE_CURRENT_LIST_FILE}\n")
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 扫描目录: ${TEST_DIR}\n\n")

# 写入测试文件列表
if(TEST_SOURCES)
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}set(TEST_SOURCES\n")
    foreach(test ${TEST_SOURCES})
        # 使用相对路径
        file(RELATIVE_PATH rel_test ${CMAKE_SOURCE_DIR} ${test})
        set(OUTPUT_CONTENT "${OUTPUT_CONTENT}    \"${rel_test}\"\n")
    endforeach()
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT})\n\n")
else()
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}set(TEST_SOURCES)\n\n")
endif()

# 添加调试信息
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 调试信息\n")
list(LENGTH TEST_SOURCES TEST_COUNT)
set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 找到 ${TEST_COUNT} 个测试文件\n")

if(TEST_SOURCES)
    set(OUTPUT_CONTENT "${OUTPUT_CONTENT}# 测试文件:\n")
    foreach(test ${TEST_SOURCES})
        get_filename_component(test_name ${test} NAME)
        set(OUTPUT_CONTENT "${OUTPUT_CONTENT}#   - ${test_name}\n")
    endforeach()
endif()

# 检查输出文件是否需要更新
set(NEED_UPDATE TRUE)
if(EXISTS ${OUTPUT_FILE})
    file(READ ${OUTPUT_FILE} EXISTING_CONTENT)
    if("${EXISTING_CONTENT}" STREQUAL "${OUTPUT_CONTENT}")
        set(NEED_UPDATE FALSE)
    endif()
endif()

# 只有在内容发生变化时才写入文件
if(NEED_UPDATE)
    file(WRITE ${OUTPUT_FILE} ${OUTPUT_CONTENT})
    message(STATUS "Updated test list: ${TEST_COUNT} test files")
    
    # 如果发现新测试文件，输出详细信息
    if(TEST_SOURCES)
        message(STATUS "Test files in ${TEST_DIR}:")
        foreach(test ${TEST_SOURCES})
            get_filename_component(test_name ${test} NAME)
            message(STATUS "  - ${test_name}")
        endforeach()
    endif()
else()
    message(STATUS "Test list is up to date")
endif()
