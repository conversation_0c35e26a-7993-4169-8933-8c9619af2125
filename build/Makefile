# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles /home/<USER>/tbox/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -P /home/<USER>/tbox/build/CMakeFiles/VerifyGlobs.cmake
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named tbox_event

# Build rule for target.
tbox_event: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 tbox_event
.PHONY : tbox_event

# fast build rule for target.
tbox_event/fast:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/build
.PHONY : tbox_event/fast

#=============================================================================
# Target rules for targets named tbox_util

# Build rule for target.
tbox_util: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 tbox_util
.PHONY : tbox_util

# fast build rule for target.
tbox_util/fast:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/build
.PHONY : tbox_util/fast

#=============================================================================
# Target rules for targets named fd_test

# Build rule for target.
fd_test: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 fd_test
.PHONY : fd_test

# fast build rule for target.
fd_test/fast:
	$(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/build
.PHONY : fd_test/fast

#=============================================================================
# Target rules for targets named fs_test

# Build rule for target.
fs_test: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 fs_test
.PHONY : fs_test

# fast build rule for target.
fs_test/fast:
	$(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/build
.PHONY : fs_test/fast

#=============================================================================
# Target rules for targets named run_tests_direct

# Build rule for target.
run_tests_direct: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 run_tests_direct
.PHONY : run_tests_direct

# fast build rule for target.
run_tests_direct/fast:
	$(MAKE) -f test/CMakeFiles/run_tests_direct.dir/build.make test/CMakeFiles/run_tests_direct.dir/build
.PHONY : run_tests_direct/fast

#=============================================================================
# Target rules for targets named string_test

# Build rule for target.
string_test: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 string_test
.PHONY : string_test

# fast build rule for target.
string_test/fast:
	$(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/build
.PHONY : string_test/fast

#=============================================================================
# Target rules for targets named clean_tests

# Build rule for target.
clean_tests: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_tests
.PHONY : clean_tests

# fast build rule for target.
clean_tests/fast:
	$(MAKE) -f test/CMakeFiles/clean_tests.dir/build.make test/CMakeFiles/clean_tests.dir/build
.PHONY : clean_tests/fast

#=============================================================================
# Target rules for targets named test_help

# Build rule for target.
test_help: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 test_help
.PHONY : test_help

# fast build rule for target.
test_help/fast:
	$(MAKE) -f test/CMakeFiles/test_help.dir/build.make test/CMakeFiles/test_help.dir/build
.PHONY : test_help/fast

#=============================================================================
# Target rules for targets named run_tests

# Build rule for target.
run_tests: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 run_tests
.PHONY : run_tests

# fast build rule for target.
run_tests/fast:
	$(MAKE) -f test/CMakeFiles/run_tests.dir/build.make test/CMakeFiles/run_tests.dir/build
.PHONY : run_tests/fast

event/common_loop.o: event/common_loop.cpp.o

.PHONY : event/common_loop.o

# target to build an object file
event/common_loop.cpp.o:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/common_loop.cpp.o
.PHONY : event/common_loop.cpp.o

event/common_loop.i: event/common_loop.cpp.i

.PHONY : event/common_loop.i

# target to preprocess a source file
event/common_loop.cpp.i:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/common_loop.cpp.i
.PHONY : event/common_loop.cpp.i

event/common_loop.s: event/common_loop.cpp.s

.PHONY : event/common_loop.s

# target to generate assembly for a file
event/common_loop.cpp.s:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/common_loop.cpp.s
.PHONY : event/common_loop.cpp.s

event/epoll_fd_event.o: event/epoll_fd_event.cpp.o

.PHONY : event/epoll_fd_event.o

# target to build an object file
event/epoll_fd_event.cpp.o:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.o
.PHONY : event/epoll_fd_event.cpp.o

event/epoll_fd_event.i: event/epoll_fd_event.cpp.i

.PHONY : event/epoll_fd_event.i

# target to preprocess a source file
event/epoll_fd_event.cpp.i:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.i
.PHONY : event/epoll_fd_event.cpp.i

event/epoll_fd_event.s: event/epoll_fd_event.cpp.s

.PHONY : event/epoll_fd_event.s

# target to generate assembly for a file
event/epoll_fd_event.cpp.s:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/epoll_fd_event.cpp.s
.PHONY : event/epoll_fd_event.cpp.s

event/epoll_loop.o: event/epoll_loop.cpp.o

.PHONY : event/epoll_loop.o

# target to build an object file
event/epoll_loop.cpp.o:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.o
.PHONY : event/epoll_loop.cpp.o

event/epoll_loop.i: event/epoll_loop.cpp.i

.PHONY : event/epoll_loop.i

# target to preprocess a source file
event/epoll_loop.cpp.i:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.i
.PHONY : event/epoll_loop.cpp.i

event/epoll_loop.s: event/epoll_loop.cpp.s

.PHONY : event/epoll_loop.s

# target to generate assembly for a file
event/epoll_loop.cpp.s:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/epoll_loop.cpp.s
.PHONY : event/epoll_loop.cpp.s

event/loop.o: event/loop.cpp.o

.PHONY : event/loop.o

# target to build an object file
event/loop.cpp.o:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/loop.cpp.o
.PHONY : event/loop.cpp.o

event/loop.i: event/loop.cpp.i

.PHONY : event/loop.i

# target to preprocess a source file
event/loop.cpp.i:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/loop.cpp.i
.PHONY : event/loop.cpp.i

event/loop.s: event/loop.cpp.s

.PHONY : event/loop.s

# target to generate assembly for a file
event/loop.cpp.s:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/loop.cpp.s
.PHONY : event/loop.cpp.s

event/stat.o: event/stat.cpp.o

.PHONY : event/stat.o

# target to build an object file
event/stat.cpp.o:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/stat.cpp.o
.PHONY : event/stat.cpp.o

event/stat.i: event/stat.cpp.i

.PHONY : event/stat.i

# target to preprocess a source file
event/stat.cpp.i:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/stat.cpp.i
.PHONY : event/stat.cpp.i

event/stat.s: event/stat.cpp.s

.PHONY : event/stat.s

# target to generate assembly for a file
event/stat.cpp.s:
	$(MAKE) -f CMakeFiles/tbox_event.dir/build.make CMakeFiles/tbox_event.dir/event/stat.cpp.s
.PHONY : event/stat.cpp.s

util/fd.o: util/fd.cc.o

.PHONY : util/fd.o

# target to build an object file
util/fd.cc.o:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/fd.cc.o
.PHONY : util/fd.cc.o

util/fd.i: util/fd.cc.i

.PHONY : util/fd.i

# target to preprocess a source file
util/fd.cc.i:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/fd.cc.i
.PHONY : util/fd.cc.i

util/fd.s: util/fd.cc.s

.PHONY : util/fd.s

# target to generate assembly for a file
util/fd.cc.s:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/fd.cc.s
.PHONY : util/fd.cc.s

util/fs.o: util/fs.cc.o

.PHONY : util/fs.o

# target to build an object file
util/fs.cc.o:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/fs.cc.o
.PHONY : util/fs.cc.o

util/fs.i: util/fs.cc.i

.PHONY : util/fs.i

# target to preprocess a source file
util/fs.cc.i:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/fs.cc.i
.PHONY : util/fs.cc.i

util/fs.s: util/fs.cc.s

.PHONY : util/fs.s

# target to generate assembly for a file
util/fs.cc.s:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/fs.cc.s
.PHONY : util/fs.cc.s

util/json/ison.o: util/json/ison.cc.o

.PHONY : util/json/ison.o

# target to build an object file
util/json/ison.cc.o:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/json/ison.cc.o
.PHONY : util/json/ison.cc.o

util/json/ison.i: util/json/ison.cc.i

.PHONY : util/json/ison.i

# target to preprocess a source file
util/json/ison.cc.i:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/json/ison.cc.i
.PHONY : util/json/ison.cc.i

util/json/ison.s: util/json/ison.cc.s

.PHONY : util/json/ison.s

# target to generate assembly for a file
util/json/ison.cc.s:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/json/ison.cc.s
.PHONY : util/json/ison.cc.s

util/string.o: util/string.cc.o

.PHONY : util/string.o

# target to build an object file
util/string.cc.o:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/string.cc.o
.PHONY : util/string.cc.o

util/string.i: util/string.cc.i

.PHONY : util/string.i

# target to preprocess a source file
util/string.cc.i:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/string.cc.i
.PHONY : util/string.cc.i

util/string.s: util/string.cc.s

.PHONY : util/string.s

# target to generate assembly for a file
util/string.cc.s:
	$(MAKE) -f CMakeFiles/tbox_util.dir/build.make CMakeFiles/tbox_util.dir/util/string.cc.s
.PHONY : util/string.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... tbox_event"
	@echo "... tbox_util"
	@echo "... fd_test"
	@echo "... fs_test"
	@echo "... run_tests_direct"
	@echo "... string_test"
	@echo "... clean_tests"
	@echo "... test_help"
	@echo "... run_tests"
	@echo "... event/common_loop.o"
	@echo "... event/common_loop.i"
	@echo "... event/common_loop.s"
	@echo "... event/epoll_fd_event.o"
	@echo "... event/epoll_fd_event.i"
	@echo "... event/epoll_fd_event.s"
	@echo "... event/epoll_loop.o"
	@echo "... event/epoll_loop.i"
	@echo "... event/epoll_loop.s"
	@echo "... event/loop.o"
	@echo "... event/loop.i"
	@echo "... event/loop.s"
	@echo "... event/stat.o"
	@echo "... event/stat.i"
	@echo "... event/stat.s"
	@echo "... util/fd.o"
	@echo "... util/fd.i"
	@echo "... util/fd.s"
	@echo "... util/fs.o"
	@echo "... util/fs.i"
	@echo "... util/fs.s"
	@echo "... util/json/ison.o"
	@echo "... util/json/ison.i"
	@echo "... util/json/ison.s"
	@echo "... util/string.o"
	@echo "... util/string.i"
	@echo "... util/string.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -P /home/<USER>/tbox/build/CMakeFiles/VerifyGlobs.cmake
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

