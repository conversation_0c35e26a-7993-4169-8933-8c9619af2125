# 测试配置文件
# 该文件具有扩展性，能够自动发现和构建测试文件

# 检查是否找到了GTest
if(NOT (GTest_FOUND OR GTEST_FOUND))
    message(WARNING "GTest not found. Tests will not be built.")
    message(STATUS "To install GTest on Ubuntu/Debian: sudo apt-get install libgtest-dev")
    message(STATUS "To install GTest on CentOS/RHEL: sudo yum install gtest-devel")
    message(STATUS "To install GTest on macOS: brew install googletest")
    return()
endif()

# 设置GTest链接库
if(GTest_FOUND)
    set(GTEST_LIBRARIES GTest::gtest GTest::gtest_main)
else()
    # 使用pkg-config找到的GTest
    set(GTEST_LIBRARIES ${GTEST_LIBRARIES} ${GTEST_MAIN_LIBRARIES})
    include_directories(${GTEST_INCLUDE_DIRS})
    link_directories(${GTEST_LIBRARY_DIRS})
endif()

# 自动发现测试文件（支持自动重新配置）
file(GLOB_RECURSE TEST_SOURCES
    CONFIGURE_DEPENDS
    "${CMAKE_CURRENT_SOURCE_DIR}/*_test.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/*_test.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*_test.cxx"
    "${CMAKE_CURRENT_SOURCE_DIR}/*_test.c++"
    "${CMAKE_CURRENT_SOURCE_DIR}/test_*.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/test_*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/test_*.cxx"
    "${CMAKE_CURRENT_SOURCE_DIR}/test_*.c++"
)

if(NOT TEST_SOURCES)
    message(WARNING "No test files found in ${CMAKE_CURRENT_SOURCE_DIR}")
    return()
endif()

message(STATUS "Found test files:")
foreach(test_file ${TEST_SOURCES})
    get_filename_component(test_name ${test_file} NAME_WE)
    message(STATUS "  - ${test_name}")
endforeach()

# 为每个测试文件创建独立的可执行文件和测试
foreach(test_file ${TEST_SOURCES})
    # 获取测试文件名（不含扩展名）
    get_filename_component(test_name ${test_file} NAME_WE)

    # 创建测试可执行文件
    add_executable(${test_name} ${test_file})

    # 设置包含目录
    target_include_directories(${test_name} PRIVATE ${CMAKE_SOURCE_DIR})

    # 链接必要的库
    target_link_libraries(${test_name} ${GTEST_LIBRARIES})

    # 链接项目库（如果存在）
    if(TARGET tbox_util)
        target_link_libraries(${test_name} tbox_util)
    endif()

    if(TARGET tbox_base)
        target_link_libraries(${test_name} tbox_base)
    endif()

    # 添加系统库（如果需要）
    target_link_libraries(${test_name} pthread)

    # 设置可执行文件属性
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/tests
        CXX_STANDARD ${CMAKE_CXX_STANDARD}
        CXX_STANDARD_REQUIRED ON
    )

    # 添加到CTest
    add_test(NAME ${test_name} COMMAND ${test_name})

    # 设置测试属性
    set_tests_properties(${test_name} PROPERTIES
        TIMEOUT 30
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin/tests
        ENVIRONMENT "GTEST_COLOR=yes"
    )
endforeach()

# 创建一个运行所有测试的目标（通过CTest）
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --verbose
    DEPENDS ${TEST_SOURCES}
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running all tests via CTest"
)

# 创建一个直接运行测试的目标（彩色输出，无序号）
add_custom_target(run_tests_direct
    COMMENT "Running all tests directly (with colors)"
)

# 为每个测试添加直接运行命令（简化版本，避免重复输出）
foreach(test_file ${TEST_SOURCES})
    get_filename_component(test_name ${test_file} NAME_WE)
    add_custom_command(TARGET run_tests_direct POST_BUILD
        COMMAND echo "=== Running ${test_name} ==="
        COMMAND ${CMAKE_BINARY_DIR}/bin/tests/${test_name} || true
        COMMAND echo ""
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin/tests
        VERBATIM
    )
endforeach()

# 创建一个清理测试输出的目标
add_custom_target(clean_tests
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/bin/tests
    COMMENT "Cleaning test binaries"
)

# 添加一个帮助目标，显示如何使用测试
add_custom_target(test_help
    COMMAND ${CMAKE_COMMAND} -E echo ""
    COMMAND ${CMAKE_COMMAND} -E echo "=== Test Usage ==="
    COMMAND ${CMAKE_COMMAND} -E echo "Build tests:              make"
    COMMAND ${CMAKE_COMMAND} -E echo "Run all tests (CTest):    make test"
    COMMAND ${CMAKE_COMMAND} -E echo "Run tests verbose:        make run_tests"
    COMMAND ${CMAKE_COMMAND} -E echo "Run tests direct (color): make run_tests_direct"
    COMMAND ${CMAKE_COMMAND} -E echo "Run specific test:        ./bin/tests/<test_name>"
    COMMAND ${CMAKE_COMMAND} -E echo "Clean test binaries:      make clean_tests"
    COMMAND ${CMAKE_COMMAND} -E echo ""
    COMMAND ${CMAKE_COMMAND} -E echo "Test Output Differences:"
    COMMAND ${CMAKE_COMMAND} -E echo "  make test           - CTest format with numbers, no colors"
    COMMAND ${CMAKE_COMMAND} -E echo "  make run_tests_direct - Direct execution with colors"
    COMMAND ${CMAKE_COMMAND} -E echo "  ./bin/tests/xxx     - Individual test with colors"
    COMMAND ${CMAKE_COMMAND} -E echo ""
    COMMAND ${CMAKE_COMMAND} -E echo "⚠️  Important: Only run ONE test command at a time to avoid duplicate output!"
    COMMAND ${CMAKE_COMMAND} -E echo ""
    COMMAND ${CMAKE_COMMAND} -E echo "Available tests:"
    VERBATIM
)

# 为帮助目标添加测试列表
foreach(test_file ${TEST_SOURCES})
    get_filename_component(test_name ${test_file} NAME_WE)
    add_custom_command(TARGET test_help POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E echo "  - ${test_name}"
        VERBATIM
    )
endforeach()

message(STATUS "Test configuration completed. Use 'make test_help' for usage information.")
