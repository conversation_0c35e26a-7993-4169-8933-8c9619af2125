#ifndef EVENT_FD_ITEM_H_
#define EVENT_FD_ITEM_H_

#include <functional>

#include "event.h"

namespace tbox {
namespace event {

class FdEvent : public Event {
  public:
    enum EventTypes {
        kReadEvent   = 0x01,    //!< 可读事件
        kWriteEvent  = 0x02,    //!< 可写事件
        kExceptEvent = 0x04,    //!< 异常事件
    };

    using Event::Event;

    virtual bool initialize(int fd, short events, Mode mode) = 0;

    using CallbackFunc = std::function<void (short events)>;
    virtual void setCallback(CallbackFunc &&cb) = 0;

  public:
    virtual ~FdEvent() { }
};

}
}

#endif //EVENT_FD_ITEM_H_
