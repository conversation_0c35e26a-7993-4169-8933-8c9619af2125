# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.16

# tbox_base_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.c++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_base_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.cc")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_base_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_base_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.cxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_base_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.h")
set(OLD_GLOB
  "/home/<USER>/tbox/base/cabinet_token.h"
  "/home/<USER>/tbox/base/defines.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_base_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.h++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_base_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.hpp")
set(OLD_GLOB
  "/home/<USER>/tbox/base/cabinet.hpp"
  "/home/<USER>/tbox/base/object_pool.hpp"
  "/home/<USER>/tbox/base/scope_exit.hpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_base_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/base/*.hxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.c++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.cc")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.cpp")
set(OLD_GLOB
  "/home/<USER>/tbox/event/common_loop.cpp"
  "/home/<USER>/tbox/event/epoll_fd_event.cpp"
  "/home/<USER>/tbox/event/epoll_loop.cpp"
  "/home/<USER>/tbox/event/loop.cpp"
  "/home/<USER>/tbox/event/stat.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.cxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.h")
set(OLD_GLOB
  "/home/<USER>/tbox/event/common_loop.h"
  "/home/<USER>/tbox/event/epoll_fd_event.h"
  "/home/<USER>/tbox/event/epoll_loop.h"
  "/home/<USER>/tbox/event/event.h"
  "/home/<USER>/tbox/event/fd_event.h"
  "/home/<USER>/tbox/event/forward.h"
  "/home/<USER>/tbox/event/loop.h"
  "/home/<USER>/tbox/event/stat.h"
  "/home/<USER>/tbox/event/types.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.h++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.hpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_event_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/event/*.hxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/*_test.c++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/*_test.cc")
set(OLD_GLOB
  "/home/<USER>/tbox/test/fd_test.cc"
  "/home/<USER>/tbox/test/fs_test.cc"
  "/home/<USER>/tbox/test/string_test.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/*_test.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/*_test.cxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/test_*.c++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/test_*.cc")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/test_*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# TEST_SOURCES at test/CMakeLists.txt:24 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/test/test_*.cxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.c++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.cc")
set(OLD_GLOB
  "/home/<USER>/tbox/util/fd.cc"
  "/home/<USER>/tbox/util/fs.cc"
  "/home/<USER>/tbox/util/json/ison.cc"
  "/home/<USER>/tbox/util/string.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_SOURCES at CMakeLists.txt:43 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.cxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.h")
set(OLD_GLOB
  "/home/<USER>/tbox/util/buffer.h"
  "/home/<USER>/tbox/util/fd.h"
  "/home/<USER>/tbox/util/fs.h"
  "/home/<USER>/tbox/util/json/json.h"
  "/home/<USER>/tbox/util/string.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.h++")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.hpp")
set(OLD_GLOB
  "/home/<USER>/tbox/util/json/json.hpp"
  "/home/<USER>/tbox/util/json/json_fwd.hpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()

# tbox_util_HEADERS at CMakeLists.txt:50 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/tbox/util/*.hxx")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/tbox/build/CMakeFiles/cmake.verify_globs")
endif()
