# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles /home/<USER>/tbox/build/test/CMakeFiles/progress.marks
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tbox/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -P /home/<USER>/tbox/build/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
test/CMakeFiles/fd_test.dir/rule:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/fd_test.dir/rule
.PHONY : test/CMakeFiles/fd_test.dir/rule

# Convenience name for target.
fd_test: test/CMakeFiles/fd_test.dir/rule

.PHONY : fd_test

# fast build rule for target.
fd_test/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/build
.PHONY : fd_test/fast

# Convenience name for target.
test/CMakeFiles/fs_test.dir/rule:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/fs_test.dir/rule
.PHONY : test/CMakeFiles/fs_test.dir/rule

# Convenience name for target.
fs_test: test/CMakeFiles/fs_test.dir/rule

.PHONY : fs_test

# fast build rule for target.
fs_test/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/build
.PHONY : fs_test/fast

# Convenience name for target.
test/CMakeFiles/run_tests_direct.dir/rule:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/run_tests_direct.dir/rule
.PHONY : test/CMakeFiles/run_tests_direct.dir/rule

# Convenience name for target.
run_tests_direct: test/CMakeFiles/run_tests_direct.dir/rule

.PHONY : run_tests_direct

# fast build rule for target.
run_tests_direct/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/run_tests_direct.dir/build.make test/CMakeFiles/run_tests_direct.dir/build
.PHONY : run_tests_direct/fast

# Convenience name for target.
test/CMakeFiles/string_test.dir/rule:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/string_test.dir/rule
.PHONY : test/CMakeFiles/string_test.dir/rule

# Convenience name for target.
string_test: test/CMakeFiles/string_test.dir/rule

.PHONY : string_test

# fast build rule for target.
string_test/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/build
.PHONY : string_test/fast

# Convenience name for target.
test/CMakeFiles/clean_tests.dir/rule:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/clean_tests.dir/rule
.PHONY : test/CMakeFiles/clean_tests.dir/rule

# Convenience name for target.
clean_tests: test/CMakeFiles/clean_tests.dir/rule

.PHONY : clean_tests

# fast build rule for target.
clean_tests/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/clean_tests.dir/build.make test/CMakeFiles/clean_tests.dir/build
.PHONY : clean_tests/fast

# Convenience name for target.
test/CMakeFiles/test_help.dir/rule:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/test_help.dir/rule
.PHONY : test/CMakeFiles/test_help.dir/rule

# Convenience name for target.
test_help: test/CMakeFiles/test_help.dir/rule

.PHONY : test_help

# fast build rule for target.
test_help/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/test_help.dir/build.make test/CMakeFiles/test_help.dir/build
.PHONY : test_help/fast

# Convenience name for target.
test/CMakeFiles/run_tests.dir/rule:
	cd /home/<USER>/tbox/build && $(MAKE) -f CMakeFiles/Makefile2 test/CMakeFiles/run_tests.dir/rule
.PHONY : test/CMakeFiles/run_tests.dir/rule

# Convenience name for target.
run_tests: test/CMakeFiles/run_tests.dir/rule

.PHONY : run_tests

# fast build rule for target.
run_tests/fast:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/run_tests.dir/build.make test/CMakeFiles/run_tests.dir/build
.PHONY : run_tests/fast

fd_test.o: fd_test.cc.o

.PHONY : fd_test.o

# target to build an object file
fd_test.cc.o:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/fd_test.cc.o
.PHONY : fd_test.cc.o

fd_test.i: fd_test.cc.i

.PHONY : fd_test.i

# target to preprocess a source file
fd_test.cc.i:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/fd_test.cc.i
.PHONY : fd_test.cc.i

fd_test.s: fd_test.cc.s

.PHONY : fd_test.s

# target to generate assembly for a file
fd_test.cc.s:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fd_test.dir/build.make test/CMakeFiles/fd_test.dir/fd_test.cc.s
.PHONY : fd_test.cc.s

fs_test.o: fs_test.cc.o

.PHONY : fs_test.o

# target to build an object file
fs_test.cc.o:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/fs_test.cc.o
.PHONY : fs_test.cc.o

fs_test.i: fs_test.cc.i

.PHONY : fs_test.i

# target to preprocess a source file
fs_test.cc.i:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/fs_test.cc.i
.PHONY : fs_test.cc.i

fs_test.s: fs_test.cc.s

.PHONY : fs_test.s

# target to generate assembly for a file
fs_test.cc.s:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/fs_test.dir/build.make test/CMakeFiles/fs_test.dir/fs_test.cc.s
.PHONY : fs_test.cc.s

string_test.o: string_test.cc.o

.PHONY : string_test.o

# target to build an object file
string_test.cc.o:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/string_test.cc.o
.PHONY : string_test.cc.o

string_test.i: string_test.cc.i

.PHONY : string_test.i

# target to preprocess a source file
string_test.cc.i:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/string_test.cc.i
.PHONY : string_test.cc.i

string_test.s: string_test.cc.s

.PHONY : string_test.s

# target to generate assembly for a file
string_test.cc.s:
	cd /home/<USER>/tbox/build && $(MAKE) -f test/CMakeFiles/string_test.dir/build.make test/CMakeFiles/string_test.dir/string_test.cc.s
.PHONY : string_test.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... fd_test"
	@echo "... test"
	@echo "... fs_test"
	@echo "... run_tests_direct"
	@echo "... string_test"
	@echo "... clean_tests"
	@echo "... test_help"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... run_tests"
	@echo "... list_install_components"
	@echo "... install"
	@echo "... install/local"
	@echo "... fd_test.o"
	@echo "... fd_test.i"
	@echo "... fd_test.s"
	@echo "... fs_test.o"
	@echo "... fs_test.i"
	@echo "... fs_test.s"
	@echo "... string_test.o"
	@echo "... string_test.i"
	@echo "... string_test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -P /home/<USER>/tbox/build/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

