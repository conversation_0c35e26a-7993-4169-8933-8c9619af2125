# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tbox

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tbox/build

# Include any dependencies generated for this target.
include CMakeFiles/tbox_util.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/tbox_util.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/tbox_util.dir/flags.make

CMakeFiles/tbox_util.dir/util/fd.cc.o: CMakeFiles/tbox_util.dir/flags.make
CMakeFiles/tbox_util.dir/util/fd.cc.o: ../util/fd.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/tbox_util.dir/util/fd.cc.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_util.dir/util/fd.cc.o -c /home/<USER>/tbox/util/fd.cc

CMakeFiles/tbox_util.dir/util/fd.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_util.dir/util/fd.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/util/fd.cc > CMakeFiles/tbox_util.dir/util/fd.cc.i

CMakeFiles/tbox_util.dir/util/fd.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_util.dir/util/fd.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/util/fd.cc -o CMakeFiles/tbox_util.dir/util/fd.cc.s

CMakeFiles/tbox_util.dir/util/fs.cc.o: CMakeFiles/tbox_util.dir/flags.make
CMakeFiles/tbox_util.dir/util/fs.cc.o: ../util/fs.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/tbox_util.dir/util/fs.cc.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_util.dir/util/fs.cc.o -c /home/<USER>/tbox/util/fs.cc

CMakeFiles/tbox_util.dir/util/fs.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_util.dir/util/fs.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/util/fs.cc > CMakeFiles/tbox_util.dir/util/fs.cc.i

CMakeFiles/tbox_util.dir/util/fs.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_util.dir/util/fs.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/util/fs.cc -o CMakeFiles/tbox_util.dir/util/fs.cc.s

CMakeFiles/tbox_util.dir/util/json/ison.cc.o: CMakeFiles/tbox_util.dir/flags.make
CMakeFiles/tbox_util.dir/util/json/ison.cc.o: ../util/json/ison.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/tbox_util.dir/util/json/ison.cc.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_util.dir/util/json/ison.cc.o -c /home/<USER>/tbox/util/json/ison.cc

CMakeFiles/tbox_util.dir/util/json/ison.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_util.dir/util/json/ison.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/util/json/ison.cc > CMakeFiles/tbox_util.dir/util/json/ison.cc.i

CMakeFiles/tbox_util.dir/util/json/ison.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_util.dir/util/json/ison.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/util/json/ison.cc -o CMakeFiles/tbox_util.dir/util/json/ison.cc.s

CMakeFiles/tbox_util.dir/util/string.cc.o: CMakeFiles/tbox_util.dir/flags.make
CMakeFiles/tbox_util.dir/util/string.cc.o: ../util/string.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/tbox_util.dir/util/string.cc.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tbox_util.dir/util/string.cc.o -c /home/<USER>/tbox/util/string.cc

CMakeFiles/tbox_util.dir/util/string.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tbox_util.dir/util/string.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/tbox/util/string.cc > CMakeFiles/tbox_util.dir/util/string.cc.i

CMakeFiles/tbox_util.dir/util/string.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tbox_util.dir/util/string.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/tbox/util/string.cc -o CMakeFiles/tbox_util.dir/util/string.cc.s

# Object files for target tbox_util
tbox_util_OBJECTS = \
"CMakeFiles/tbox_util.dir/util/fd.cc.o" \
"CMakeFiles/tbox_util.dir/util/fs.cc.o" \
"CMakeFiles/tbox_util.dir/util/json/ison.cc.o" \
"CMakeFiles/tbox_util.dir/util/string.cc.o"

# External object files for target tbox_util
tbox_util_EXTERNAL_OBJECTS =

lib/libtbox_util.a: CMakeFiles/tbox_util.dir/util/fd.cc.o
lib/libtbox_util.a: CMakeFiles/tbox_util.dir/util/fs.cc.o
lib/libtbox_util.a: CMakeFiles/tbox_util.dir/util/json/ison.cc.o
lib/libtbox_util.a: CMakeFiles/tbox_util.dir/util/string.cc.o
lib/libtbox_util.a: CMakeFiles/tbox_util.dir/build.make
lib/libtbox_util.a: CMakeFiles/tbox_util.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/tbox/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library lib/libtbox_util.a"
	$(CMAKE_COMMAND) -P CMakeFiles/tbox_util.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tbox_util.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/tbox_util.dir/build: lib/libtbox_util.a

.PHONY : CMakeFiles/tbox_util.dir/build

CMakeFiles/tbox_util.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/tbox_util.dir/cmake_clean.cmake
.PHONY : CMakeFiles/tbox_util.dir/clean

CMakeFiles/tbox_util.dir/depend:
	cd /home/<USER>/tbox/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/tbox /home/<USER>/tbox /home/<USER>/tbox/build /home/<USER>/tbox/build /home/<USER>/tbox/build/CMakeFiles/tbox_util.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/tbox_util.dir/depend

